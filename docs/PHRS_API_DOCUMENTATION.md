# PHRS代币支付系统 API文档

## 概述

PHRS代币支付系统是Wolf Fun游戏的新支付方式，基于Pharos网络（EVM兼容的L1区块链）。用户可以通过充值PHRS代币到游戏系统，然后使用PHRS余额购买游戏内道具。

## 系统架构

```
用户钱包 → PHRS充值合约 → 事件监听服务 → 游戏后端 → 用户余额更新
                                    ↓
用户购买道具 ← PHRS支付API ← 游戏后端 ← 用户PHRS余额
```

## 环境变量配置

```bash
# Pharos网络配置
PHAROS_RPC_URL=https://rpc.pharos.network
PHAROS_TESTNET_RPC_URL=https://testnet-rpc.pharos.network

# PHRS代币和合约地址
PHRS_TOKEN_ADDRESS=0x...
PHRS_DEPOSIT_CONTRACT_ADDRESS=0x...

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/wolf_fun

# JWT密钥
JWT_SECRET=your-jwt-secret-here
```

## API接口

### 1. PHRS充值相关接口

#### 1.1 绑定PHRS钱包地址

**POST** `/api/phrs-deposit/bind-wallet`

绑定用户的PHRS钱包地址，用于接收充值。

**请求头：**
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**请求体：**
```json
{
  "phrsWalletAddress": "******************************************"
}
```

**响应：**
```json
{
  "ok": true,
  "message": "PHRS wallet bound successfully",
  "data": {
    "phrsWalletAddress": "******************************************",
    "phrsBalance": "0.000"
  }
}
```

#### 1.2 获取用户充值记录

**GET** `/api/phrs-deposit/deposits`

获取用户的PHRS充值历史记录。

**请求头：**
```
Authorization: Bearer <JWT_TOKEN>
```

**查询参数：**
- `page` (可选): 页码，默认为1
- `limit` (可选): 每页数量，默认为20
- `status` (可选): 充值状态过滤 (PENDING, CONFIRMED, FAILED)

**响应：**
```json
{
  "ok": true,
  "data": {
    "deposits": [
      {
        "id": 1,
        "amount": "100.000",
        "userAddress": "******************************************",
        "transactionHash": "0xabc...",
        "blockNumber": 1000,
        "blockTimestamp": "2025-07-17T10:00:00.000Z",
        "status": "CONFIRMED",
        "confirmations": 12,
        "processedAt": "2025-07-17T10:01:00.000Z",
        "createdAt": "2025-07-17T10:00:30.000Z"
      }
    ],
    "pagination": {
      "total": 1,
      "page": 1,
      "limit": 20,
      "totalPages": 1
    }
  }
}
```

#### 1.3 手动同步PHRS余额

**POST** `/api/phrs-deposit/sync-balance`

手动同步用户的PHRS余额。

**请求头：**
```
Authorization: Bearer <JWT_TOKEN>
```

**响应：**
```json
{
  "ok": true,
  "message": "PHRS balance synced successfully",
  "data": {
    "phrsBalance": "150.000",
    "lastPhrsUpdateTime": "2025-07-17T10:05:00.000Z"
  }
}
```

### 2. PHRS支付相关接口

#### 2.1 使用PHRS购买道具

**POST** `/api/phrs-payment/purchase`

使用PHRS余额购买游戏道具。

**请求头：**
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**请求体：**
```json
{
  "productId": 1
}
```

**响应：**
```json
{
  "ok": true,
  "message": "Purchase completed successfully",
  "data": {
    "purchaseId": 123,
    "productName": "2x Speed Boost (1 Hour)",
    "phrsPaid": "10.000",
    "remainingBalance": "140.000",
    "product": {
      "id": 1,
      "name": "2x Speed Boost (1 Hour)",
      "type": "speed_boost"
    }
  }
}
```

#### 2.2 获取PHRS余额和购买历史

**GET** `/api/phrs-payment/balance`

获取用户的PHRS余额和最近购买记录。

**请求头：**
```
Authorization: Bearer <JWT_TOKEN>
```

**响应：**
```json
{
  "ok": true,
  "data": {
    "phrsBalance": "140.000",
    "phrsWalletAddress": "******************************************",
    "lastPhrsUpdateTime": "2025-07-17T10:05:00.000Z",
    "recentPurchases": [
      {
        "id": 123,
        "productName": "2x Speed Boost (1 Hour)",
        "productType": "speed_boost",
        "amount": 10.0,
        "purchaseDate": "2025-07-17T10:10:00.000Z",
        "createdAt": "2025-07-17T10:10:00.000Z"
      }
    ]
  }
}
```

#### 2.3 获取支持PHRS支付的商品列表

**GET** `/api/phrs-payment/products`

获取所有支持PHRS支付的游戏道具。

**请求头：**
```
Authorization: Bearer <JWT_TOKEN>
```

**响应：**
```json
{
  "ok": true,
  "data": {
    "products": [
      {
        "id": 1,
        "productId": "speed_boost_2x_1h",
        "name": "2x Speed Boost (1 Hour)",
        "type": "speed_boost",
        "phrsPrice": 10.0,
        "multiplier": 2,
        "duration": 3600,
        "quantity": 1,
        "dailyLimit": 3,
        "accountLimit": null,
        "description": "Double your delivery speed for 1 hour"
      }
    ]
  }
}
```

### 3. 用户信息接口更新

#### 3.1 获取用户信息

**GET** `/api/user/me`

获取用户信息，现在包含PHRS相关数据。

**请求头：**
```
Authorization: Bearer <JWT_TOKEN>
```

**响应（新增字段）：**
```json
{
  "ok": true,
  "id": 1,
  "username": "testuser",
  "gem": "100.000",
  "diamond": "50.000",
  "milk": 50,
  "phrsBalance": "140.000",
  "phrsWalletAddress": "******************************************",
  "lastPhrsUpdateTime": "2025-07-17T10:05:00.000Z",
  "phrsStats": {
    "totalDeposits": 2,
    "totalDepositAmount": "200.000",
    "totalPurchases": 1,
    "totalSpentAmount": "10.000"
  }
}
```

### 4. 健康检查接口

#### 4.1 PHRS充值服务健康检查

**GET** `/api/phrs-deposit/health`

检查PHRS充值监听服务的状态。

**响应：**
```json
{
  "ok": true,
  "data": {
    "service": {
      "isListening": true,
      "contractAddress": "0x...",
      "lastProcessedBlock": 1000,
      "providerUrl": "https://rpc.pharos.network"
    },
    "monitor": {
      "isRunning": true,
      "cronSchedule": "*/5 * * * *"
    },
    "timestamp": "2025-07-17T10:00:00.000Z"
  }
}
```

#### 4.2 PHRS支付服务健康检查

**GET** `/api/phrs-payment/health`

检查PHRS支付服务的状态。

**响应：**
```json
{
  "ok": true,
  "data": {
    "service": "PHRS Payment Service",
    "status": "running",
    "timestamp": "2025-07-17T10:00:00.000Z"
  }
}
```

## 错误处理

### 常见错误码

- `400` - 请求参数错误
- `401` - 未认证或token无效
- `404` - 资源不存在
- `410` - 接口已弃用（DappPortal支付）
- `500` - 服务器内部错误

### 错误响应格式

```json
{
  "ok": false,
  "message": "Error message",
  "details": "Detailed error information"
}
```

### PHRS支付特定错误

#### 余额不足
```json
{
  "ok": false,
  "message": "Insufficient PHRS balance",
  "data": {
    "required": "10.000",
    "available": "5.000"
  }
}
```

#### 购买限制
```json
{
  "ok": false,
  "message": "Daily purchase limit exceeded",
  "productType": "speed_boost",
  "limit": 3,
  "purchased": 3
}
```

## 数值精度

- 所有PHRS金额使用BigNumber.js处理，确保精度
- API返回的金额格式为字符串，保留3位小数
- 支持超大数值（适用于游戏后期大额交易）

## 安全考虑

1. **JWT认证**: 所有API都需要有效的JWT token
2. **参数验证**: 使用AJV进行严格的参数验证
3. **交易原子性**: 使用数据库事务确保数据一致性
4. **重入防护**: 智能合约使用OpenZeppelin的ReentrancyGuard
5. **权限控制**: 管理员接口需要额外的权限验证

## 迁移指南

### 从DappPortal迁移到PHRS

1. **停用旧接口**: `POST /api/iap/payment/create` 返回410状态码
2. **使用新接口**: `POST /api/phrs-payment/purchase`
3. **更新前端**: 修改支付流程，使用PHRS余额而非外部支付
4. **数据迁移**: 现有购买记录保持不变，新购买使用PHRS

### 前端集成示例

```javascript
// 绑定PHRS钱包
async function bindPhrsWallet(walletAddress) {
  const response = await fetch('/api/phrs-deposit/bind-wallet', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ phrsWalletAddress: walletAddress })
  });
  return response.json();
}

// 使用PHRS购买道具
async function purchaseWithPhrs(productId) {
  const response = await fetch('/api/phrs-payment/purchase', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ productId })
  });
  return response.json();
}
```
