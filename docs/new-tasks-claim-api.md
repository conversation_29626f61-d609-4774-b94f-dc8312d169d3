# 新任务系统领取奖励 API 文档

本文档描述了新任务系统中领取任务奖励的API接口。通过该接口，用户可以领取已完成任务的奖励，包括钻石、金币、宝箱和道具等。

## 基本信息

- **接口路径**: `/api/new-tasks/claim`
- **请求方法**: POST
- **认证要求**: 需要钱包认证（walletAuthMiddleware）
- **语言支持**: 支持国际化（languageMiddleware）
- **Content-Type**: application/json

## API 接口详情

### 领取任务奖励

领取指定任务的奖励，任务必须处于已完成状态才能领取。

**请求方法**: POST

**路径**: `/api/new-tasks/claim`

**请求体参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| taskId | integer | 是 | 任务配置ID，必须大于等于1 |

**认证要求**: 
- 需要在请求头中包含有效的JWT token
- 通过 `Authorization: Bearer <token>` 方式传递

**请求体示例**:

```json
{
  "taskId": 1
}
```

## 参数验证规则

接口使用 JSON Schema 进行严格的参数验证：

```javascript
{
  type: "object",
  properties: {
    taskId: { type: "integer", minimum: 1 }
  },
  required: ["taskId"]
}
```

### 验证内容

- `taskId` 必须存在
- `taskId` 必须是整数类型
- `taskId` 必须大于等于1

## 响应格式

### 成功响应

```json
{
  "ok": true,
  "data": {
    "success": true,
    "message": "奖励领取成功",
    "rewards": {
      "diamond": 100,
      "box": 1
    },
    "chestRewards": [
      {
        "openedCount": 1,
        "chestIds": [123],
        "rewards": [
          {
            "level": 2,
            "items": [
              {
                "type": "fragment_blue",
                "amount": 4
              },
              {
                "type": "diamond",
                "amount": 519616
              }
            ]
          }
        ],
        "summary": {
          "fragment_blue": 4,
          "diamond": 519616
        },
        "levelSummary": {
          "level1": 0,
          "level2": 1,
          "level3": 0,
          "level4": 0
        },
        "shareLinks": [],
        "jackpotWinner": null
      }
    ],
    "updatedBalance": {
      "gems": 1500,
      "diamonds": 519716
    }
  },
  "message": "奖励领取成功"
}
```

## 响应字段说明

### 主要响应结构

| 字段名 | 类型 | 描述 |
|--------|------|------|
| ok | boolean | 请求是否成功 |
| data | object | 响应数据 |
| message | string | 响应消息 |

### data 对象字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 奖励领取是否成功 |
| message | string | 操作结果消息 |
| rewards | object | 直接奖励汇总 |
| chestRewards | array/undefined | 宝箱奖励详情（如果有宝箱奖励） |
| updatedBalance | object | 更新后的用户余额 |

### rewards 对象字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| diamond | number | 钻石奖励数量 |
| coin | number | 金币奖励数量（对应用户的gem字段） |
| box | number | 宝箱奖励数量 |
| item | number | 道具奖励数量 |

### updatedBalance 对象字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| gems | number | 更新后的金币余额 |
| diamonds | number | 更新后的钻石余额 |

### chestRewards 数组元素

当任务奖励包含宝箱时，会自动开启宝箱并返回详细奖励信息：

| 字段名 | 类型 | 描述 |
|--------|------|------|
| openedCount | number | 开启的宝箱数量 |
| chestIds | array | 宝箱ID列表 |
| rewards | array | 宝箱奖励详情 |
| summary | object | 奖励汇总 |
| levelSummary | object | 宝箱等级统计 |
| shareLinks | array | 高级宝箱分享链接（3级及以上） |
| jackpotWinner | object/null | 奖池中奖信息 |

### 宝箱奖励详情

每个宝箱奖励包含：

| 字段名 | 类型 | 描述 |
|--------|------|------|
| level | number | 宝箱等级（1-4） |
| items | array | 奖励物品列表 |

### 宝箱物品类型

| 类型 | 描述 | 等级分布 |
|------|------|----------|
| fragment_green | 绿色碎片 | LV1宝箱(40%概率) - 16个 |
| fragment_blue | 蓝色碎片 | LV2宝箱(30%概率) - 4个 |
| fragment_purple | 紫色碎片 | LV3宝箱(20%概率) - 1个 |
| fragment_gold | 金色碎片 | LV4宝箱(10%概率) - 1个 |
| diamond | 钻石 | 所有等级都有，数量不同 |

## 业务逻辑说明

1. **任务状态检查**: 只有状态为 `completed` 的任务才能领取奖励
2. **原子性操作**: 使用数据库事务确保奖励发放的原子性
3. **自动宝箱开启**: 宝箱奖励会自动开启并返回详细内容
4. **余额更新**: 实时更新用户的钻石和金币余额
5. **自动任务接取**: 领取奖励后会自动检查并接取新的可用任务
6. **高级宝箱分享**: 3级及以上宝箱会自动创建分享链接

## 奖励类型说明

### 直接奖励

- **钻石 (diamond)**: 新的货币类型，存储在用户钱包的 `diamond` 字段
- **金币 (coin)**: 对应用户钱包的 `gem` 字段
- **宝箱 (box)**: 会自动开启并返回详细奖励
- **道具 (item)**: 特殊道具奖励（具体实现待完善）

### 宝箱奖励

宝箱会根据概率生成不同等级的奖励：

- **LV1宝箱 (40%概率)**: 绿色碎片16个 + 钻石389,712
- **LV2宝箱 (30%概率)**: 蓝色碎片4个 + 钻石519,616  
- **LV3宝箱 (20%概率)**: 紫色碎片1个 + 钻石779,425
- **LV4宝箱 (10%概率)**: 金色碎片1个 + 钻石1,558,850

## 请求示例

### 基本请求

```bash
curl -X POST "https://api.example.com/api/new-tasks/claim" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": 1
  }'
```

### JavaScript/TypeScript 示例

```javascript
const response = await fetch('/api/new-tasks/claim', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    taskId: 1
  })
});

const result = await response.json();
if (result.ok) {
  console.log('奖励领取成功:', result.data);
  console.log('获得钻石:', result.data.rewards.diamond);
  console.log('当前余额:', result.data.updatedBalance);
} else {
  console.error('领取失败:', result.message);
}
```

## 错误响应

### 400 参数验证失败

```json
{
  "ok": false,
  "message": "参数验证失败",
  "error": "taskId 必须是大于等于1的整数"
}
```

### 400 业务逻辑错误

```json
{
  "ok": false,
  "message": "任务不存在"
}
```

```json
{
  "ok": false,
  "message": "任务不可领取奖励"
}
```

### 401 未授权

```json
{
  "ok": false,
  "message": "用户未登录"
}
```

### 500 服务器错误

```json
{
  "ok": false,
  "message": "领取奖励失败"
}
```

## 常见错误情况

1. **任务不存在**: 指定的 taskId 不存在或用户没有该任务
2. **任务未完成**: 任务状态不是 `completed`，无法领取奖励
3. **奖励已领取**: 任务奖励已经被领取过
4. **用户不存在**: 用户钱包信息不存在
5. **参数格式错误**: taskId 不是有效的整数或小于1

## 相关接口

- [获取用户任务列表 API](./new-tasks-user-api.md) - 查看任务状态和进度
- [更新任务进度 API](#) - 手动更新任务进度
- [初始化用户任务 API](#) - 初始化用户任务状态

## 注意事项

1. **事务安全**: 所有奖励发放操作都在数据库事务中执行，确保数据一致性
2. **宝箱自动开启**: 宝箱奖励会立即开启，无需额外调用开箱接口
3. **余额实时更新**: 响应中包含更新后的用户余额信息
4. **高级宝箱特殊处理**: 3级及以上宝箱会创建分享链接，可用于社交分享
5. **国际化支持**: 错误消息和成功消息支持多语言
6. **自动任务管理**: 领取奖励后系统会自动检查并接取新的可用任务
