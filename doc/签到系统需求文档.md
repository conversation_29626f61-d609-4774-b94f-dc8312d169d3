# 签到系统需求文档

## 1. 项目概述

基于现有Wolf Fun游戏项目，新增连续签到系统，为玩家提供每日登录奖励机制，提升用户粘性和活跃度。

## 2. 核心签到规则

### 2.1 签到基础规则
- **签到起始日**：玩家创建账号的自然日记为签到的第一天
- **每日限制**：每个账号每日只能签到一次
- **时间定义**：以自然日（00:00-23:59）为签到周期
- **奖励配置**：签到的具体奖励内容由配置表格决定

### 2.2 连续性规则
- **非连续计算**：中间间隔自然日不会打断签到，只记录签到次数
- **示例**：第一天签到了，第二天没点，第三天打开仍记为第二日签到

### 2.3 轮回机制
- **7天轮回**：签到7次为一个完整轮回
- **重置时间**：完成第7日签到后，次日凌晨0点重置轮回
- **重置后状态**：重新从第1天开始计算

## 3. 奖励体系设计

### 3.1 奖励类型
- **普通签到奖励**：宝箱 + 普通牛币
- **第七日特殊奖励**：金色牛币，作为七日大奖特别标识

### 3.2 奖励配置要求
- **配置化管理**：支持通过配置表格调整各天的奖励内容
- **奖励层级**：1-6天为普通奖励，第7天为特殊奖励
- **奖励组合**：每天奖励可包含多种道具组合

### 3.3 奖励发放机制
- **即时发放**：签到成功后立即发放到用户账户
- **数据记录**：记录每次签到的奖励详情和发放时间

## 4. UI界面设计需求

### 4.1 主界面签到按钮
- **位置**：显示在主界面指定位置（如图所示）
- **状态提示**：
  - 未签到：显示红点角标提醒玩家签到
  - 已签到：无角标显示
- **交互**：点击后进入签到界面

### 4.2 签到界面设计
#### 4.2.1 界面布局
- **弹窗形式**：点击主界面签到按钮弹出该界面
- **关闭功能**：点击关闭按钮关闭该界面
- **7天展示**：显示7天签到进度和奖励预览

#### 4.2.2 奖励显示规则
- **道具图标**：宝箱图标 + 货币图标 + 数量文字
- **数量格式**：货币数量显示方式与其他界面保持一致
- **数值简化**：超过4位数字显示K等符号（如10000显示为10K）
- **第七日特殊显示**：金色牛币突出显示，标识为七日大奖

#### 4.2.3 签到按钮状态
- **未签到状态**：可点击签到按钮，完成当日签到
- **已签到状态**：按钮变为"已签到"，点击无效果

## 5. 技术架构设计

### 5.1 数据模型设计
```sql
-- 用户签到记录表
CREATE TABLE user_check_in_records (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  wallet_id INT NOT NULL,
  check_in_day INT NOT NULL COMMENT '当前轮回中的签到天数(1-7)',
  check_in_date DATE NOT NULL COMMENT '签到日期',
  cycle_start_date DATE NOT NULL COMMENT '当前轮回开始日期',
  rewards_received JSON COMMENT '获得的奖励详情',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user_date (user_id, check_in_date)
);

-- 用户签到状态表
CREATE TABLE user_check_in_status (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL UNIQUE,
  wallet_id INT NOT NULL,
  current_day INT DEFAULT 1 COMMENT '当前轮回中的签到天数(1-7)',
  cycle_start_date DATE NOT NULL COMMENT '当前轮回开始日期',
  last_check_in_date DATE COMMENT '最后签到日期',
  total_check_ins INT DEFAULT 0 COMMENT '历史总签到次数',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 签到奖励配置表
CREATE TABLE check_in_reward_config (
  id INT PRIMARY KEY AUTO_INCREMENT,
  day INT NOT NULL COMMENT '签到天数(1-7)',
  reward_type VARCHAR(50) NOT NULL COMMENT '奖励类型',
  reward_amount DECIMAL(20,8) NOT NULL COMMENT '奖励数量',
  is_special_reward BOOLEAN DEFAULT FALSE COMMENT '是否为特殊奖励(第7天)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 5.2 API接口设计
```typescript
// 获取用户签到状态
GET /api/check-in/status
Response: {
  currentDay: number;        // 当前签到天数(1-7)
  todayChecked: boolean;     // 今日是否已签到
  cycleStartDate: string;    // 当前轮回开始日期
  rewardPreview: Array<{    // 7天奖励预览
    day: number;
    rewards: Array<{
      type: string;
      amount: string;
      isSpecial: boolean;
    }>;
    claimed: boolean;
  }>;
}

// 执行签到
POST /api/check-in/claim
Response: {
  success: boolean;
  checkInDay: number;        // 签到天数
  rewards: Array<{          // 获得的奖励
    type: string;
    amount: string;
  }>;
  nextCheckInDay: number;    // 下次签到天数
}

// 获取签到历史
GET /api/check-in/history?page=1&limit=10
Response: {
  records: Array<{
    date: string;
    day: number;
    rewards: Array<object>;
  }>;
  pagination: object;
}
```

### 5.3 服务层设计
```typescript
// 签到服务类
export class CheckInService {
  // 获取用户签到状态
  async getUserCheckInStatus(userId: number): Promise<CheckInStatus>;
  
  // 执行签到
  async performCheckIn(userId: number, walletId: number): Promise<CheckInResult>;
  
  // 检查轮回重置
  async checkCycleReset(userId: number): Promise<void>;
  
  // 获取奖励配置
  async getRewardConfig(): Promise<CheckInRewardConfig[]>;
}
```

## 6. 与现有系统对比分析

### 6.1 现有系统现状
- **UserDailyClaim模型**：基于推荐数量的每日奖励系统
- **dailyRewardService**：处理推荐奖励，非连续签到逻辑
- **防重复机制**：已有今日重复领取检查

### 6.2 主要差异
| 功能点 | 现有系统 | 新签到系统 |
|--------|----------|------------|
| 奖励依据 | 推荐人数 | 连续签到天数 |
| 轮回机制 | 无 | 7天轮回重置 |
| 签到记录 | 基于日期 | 基于轮回天数 |
| 特殊奖励 | 无 | 第7天金色牛币 |
| UI界面 | 简单领取 | 完整签到日历 |

### 6.3 系统兼容性
- **数据隔离**：新签到系统使用独立数据表，不影响现有推荐奖励系统
- **服务分离**：新增CheckInService，与现有dailyRewardService并行
- **API区分**：使用不同的API路径(/api/check-in/* vs /api/invite/*)

## 7. 开发任务分解

### 7.1 数据层开发
- [ ] 创建用户签到记录模型(UserCheckInRecord)
- [ ] 创建用户签到状态模型(UserCheckInStatus)  
- [ ] 创建签到奖励配置模型(CheckInRewardConfig)
- [ ] 编写数据库迁移脚本
- [ ] 初始化签到奖励配置数据

### 7.2 服务层开发
- [ ] 开发CheckInService核心签到逻辑
- [ ] 实现轮回重置机制
- [ ] 开发奖励发放服务
- [ ] 集成现有钱包系统

### 7.3 API层开发
- [ ] 签到状态查询API
- [ ] 执行签到API
- [ ] 签到历史查询API
- [ ] API参数验证和错误处理

### 7.4 定时任务开发
- [ ] 每日凌晨轮回重置任务
- [ ] 用户状态同步任务

### 7.5 前端配合
- [ ] 主界面签到按钮集成
- [ ] 签到弹窗界面开发
- [ ] 红点提醒功能
- [ ] 奖励展示组件

## 8. 测试计划

### 8.1 单元测试
- [ ] 签到逻辑测试
- [ ] 轮回重置测试
- [ ] 奖励发放测试
- [ ] 边界条件测试

### 8.2 集成测试
- [ ] API接口测试
- [ ] 数据库事务测试
- [ ] 并发签到测试

### 8.3 用户测试
- [ ] 签到流程完整性测试
- [ ] UI交互体验测试
- [ ] 性能压力测试

## 9. 上线计划

### 9.1 开发周期
- **设计阶段**：1天
- **开发阶段**：3-4天
- **测试阶段**：1-2天
- **部署上线**：1天

### 9.2 发布策略
- **灰度发布**：先对部分用户开放测试
- **数据监控**：监控签到率、奖励发放等关键指标
- **快速修复**：准备热修复方案处理紧急问题

### 9.3 风险控制
- **数据备份**：上线前完整备份用户数据
- **回滚方案**：准备快速回滚到上个版本的方案
- **监控告警**：设置关键指标监控和告警

## 10. 后续优化方向

### 10.1 功能扩展
- **月度签到**：增加月度连续签到奖励
- **签到补卡**：允许用户花费道具补签
- **社交分享**：签到完成后分享到社交媒体

### 10.2 数据分析
- **用户留存分析**：分析签到系统对用户留存的影响
- **奖励优化**：根据用户行为数据优化奖励配置
- **个性化奖励**：基于用户画像提供个性化签到奖励

---

**文档版本**：v1.0  
**创建日期**：2025-01-25  
**最后更新**：2025-01-25  
**维护人员**：开发团队
