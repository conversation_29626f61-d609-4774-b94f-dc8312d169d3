#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB } from '../config/db';
import { PhrsDeposit } from '../models';
import { phrsDepositService } from '../services/phrsDepositService';

// 加载环境变量
dotenv.config();

async function main() {
  console.log('🧪 测试事务回滚修复');
  console.log('==================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    const targetTx = '0xe8713ec116609c9ca63d198c206d9f31dbc0ee13f548d5ce301c715472b62e85';

    // 1. 检查当前记录状态
    console.log(`\n🔍 检查当前记录状态...`);
    const existingRecords = await PhrsDeposit.findAll({
      where: { transactionHash: targetTx }
    });
    console.log(`📊 现有记录数: ${existingRecords.length}`);
    if (existingRecords.length > 0) {
      existingRecords.forEach((record, index) => {
        console.log(`   ${index + 1}. ID: ${record.id}, 状态: ${record.status}, 钱包ID: ${record.walletId || 'NULL'}`);
      });
    }

    // 2. 测试重复处理（这应该触发重复键错误和事务回滚）
    console.log(`\n🔄 测试重复处理和事务回滚...`);
    
    const testPromises = [];
    for (let i = 0; i < 3; i++) {
      testPromises.push(
        phrsDepositService.testProcessBlocks(13805521).then(() => {
          return `test-${i}-success`;
        }).catch((error: any) => {
          return `test-${i}-error: ${error.message}`;
        })
      );
    }

    const results = await Promise.all(testPromises);
    console.log('📊 测试结果:');
    results.forEach((result, index) => {
      if (typeof result === 'string' && result.includes('error')) {
        console.log(`   ${index + 1}. ❌ ${result}`);
      } else {
        console.log(`   ${index + 1}. ✅ ${result}`);
      }
    });

    // 3. 检查最终记录状态
    console.log(`\n🔍 检查最终记录状态...`);
    const finalRecords = await PhrsDeposit.findAll({
      where: { transactionHash: targetTx }
    });
    console.log(`📊 最终记录数: ${finalRecords.length}`);
    if (finalRecords.length > 0) {
      finalRecords.forEach((record, index) => {
        console.log(`   ${index + 1}. ID: ${record.id}, 状态: ${record.status}, 钱包ID: ${record.walletId || 'NULL'}`);
        console.log(`      创建时间: ${record.createdAt}`);
        console.log(`      更新时间: ${record.updatedAt}`);
      });
    }

    // 4. 验证结果
    if (finalRecords.length === existingRecords.length) {
      console.log('\n✅ 事务回滚测试通过：');
      console.log('   - 没有创建重复记录');
      console.log('   - 事务正确回滚');
      console.log('   - 没有事务状态错误');
    } else {
      console.log('\n❌ 事务回滚测试失败：');
      console.log(`   - 原有记录数: ${existingRecords.length}`);
      console.log(`   - 最终记录数: ${finalRecords.length}`);
    }

    // 5. 测试并发处理
    console.log(`\n🔄 测试高并发处理...`);
    const concurrentPromises = [];
    for (let i = 0; i < 10; i++) {
      concurrentPromises.push(
        phrsDepositService.testProcessBlocks(13805521).then(() => {
          return `concurrent-${i}-success`;
        }).catch((error: any) => {
          return `concurrent-${i}-error: ${error.message}`;
        })
      );
    }

    const concurrentResults = await Promise.all(concurrentPromises);
    console.log('📊 并发测试结果:');
    let successCount = 0;
    let errorCount = 0;
    
    concurrentResults.forEach((result, index) => {
      if (typeof result === 'string' && result.includes('error')) {
        console.log(`   ${index + 1}. ❌ ${result}`);
        errorCount++;
      } else {
        console.log(`   ${index + 1}. ✅ ${result}`);
        successCount++;
      }
    });

    console.log(`\n📊 并发测试统计:`);
    console.log(`   - 成功: ${successCount}`);
    console.log(`   - 错误: ${errorCount}`);

    // 6. 最终验证
    const veryFinalRecords = await PhrsDeposit.findAll({
      where: { transactionHash: targetTx }
    });

    if (veryFinalRecords.length === existingRecords.length) {
      console.log('\n🎉 所有测试通过！');
      console.log('   - 事务回滚正常工作');
      console.log('   - 没有重复记录');
      console.log('   - 并发处理安全');
    } else {
      console.log('\n⚠️  测试发现问题：');
      console.log(`   - 记录数量变化: ${existingRecords.length} → ${veryFinalRecords.length}`);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});
