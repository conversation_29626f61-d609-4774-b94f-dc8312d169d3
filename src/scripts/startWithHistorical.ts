#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB } from '../config/db';

// 加载环境变量
dotenv.config();

async function main() {
  // 获取命令行参数
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('🔍 PHRS监控服务 - 历史交易处理');
    console.log('===============================');
    console.log('用法: npm run start:historical -- [起始区块号]');
    console.log('例如: npm run start:historical -- 13805521');
    console.log('');
    console.log('如果不提供起始区块号，将使用环境变量 PHRS_HISTORICAL_START_BLOCK');
    console.log('如果环境变量也未设置，将从当前区块开始监控');
    process.exit(1);
  }

  const startBlock = parseInt(args[0]);
  
  if (isNaN(startBlock) || startBlock < 0) {
    console.log('❌ 无效的起始区块号');
    process.exit(1);
  }

  try {
    console.log('🔍 PHRS监控服务 - 历史交易处理');
    console.log('===============================');
    console.log(`📊 设置历史起始区块: ${startBlock}`);

    // 临时设置环境变量
    process.env.PHRS_HISTORICAL_START_BLOCK = startBlock.toString();

    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 动态导入服务（在设置环境变量后）
    const { PhrsDepositService } = await import('../services/phrsDepositService');
    const phrsDepositService = new PhrsDepositService();

    // 显示当前状态
    const status = phrsDepositService.getStatus();
    console.log(`📋 合约地址: ${status.contractAddress}`);
    console.log(`📊 当前区块: ${await phrsDepositService.provider.getBlockNumber()}`);

    // 启动监控服务
    console.log('\n🚀 启动监控服务...');
    await phrsDepositService.startListening();

    console.log('\n✅ 监控服务已启动！');
    console.log('📊 服务将：');
    console.log(`   1. 首先处理从区块 ${startBlock} 开始的所有历史交易`);
    console.log('   2. 然后每10秒检查新的交易');
    console.log('   3. 按 Ctrl+C 停止服务');

    // 保持服务运行
    process.on('SIGINT', () => {
      console.log('\n⏹️  收到停止信号，正在关闭监控服务...');
      phrsDepositService.stopListening();
      console.log('👋 监控服务已停止');
      process.exit(0);
    });

    // 定期显示状态
    setInterval(async () => {
      const currentStatus = phrsDepositService.getStatus();
      const currentBlock = await phrsDepositService.provider.getBlockNumber();
      console.log(`📊 状态更新 - 当前区块: ${currentBlock}, 最后处理: ${currentStatus.lastProcessedBlock}`);
    }, 60000); // 每分钟显示一次状态

  } catch (error) {
    console.error('❌ 启动失败:', error);
    process.exit(1);
  }
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});
