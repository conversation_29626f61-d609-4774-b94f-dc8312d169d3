#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB } from '../config/db';
import { phrsDepositService } from '../services/phrsDepositService';

// 加载环境变量
dotenv.config();

async function main() {
  console.log('🧪 测试系统健壮性和竞争条件修复');
  console.log('==================================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 1. 健康检查测试
    console.log('\n🏥 执行健康检查...');
    const healthStatus = await phrsDepositService.healthCheck();
    console.log(`📊 健康状态: ${healthStatus.status}`);
    console.log('📋 详细信息:');
    console.log(`   - 网络连接: ${healthStatus.details.networkConnection ? '✅' : '❌'}`);
    console.log(`   - 数据库连接: ${healthStatus.details.databaseConnection ? '✅' : '❌'}`);
    console.log(`   - 服务运行: ${healthStatus.details.serviceRunning ? '✅' : '❌'}`);
    console.log(`   - 最后处理区块: ${healthStatus.details.lastProcessedBlock}`);
    if (healthStatus.details.currentBlock) {
      console.log(`   - 当前区块: ${healthStatus.details.currentBlock}`);
      console.log(`   - 区块延迟: ${healthStatus.details.blockLag} 个区块`);
    }

    // 2. 服务状态测试
    console.log('\n📊 获取服务状态...');
    const status = phrsDepositService.getStatus();
    console.log('📋 服务状态:');
    console.log(`   - 监听状态: ${status.isListening ? '运行中' : '已停止'}`);
    console.log(`   - 合约地址: ${status.contractAddress}`);
    console.log(`   - 最后处理区块: ${status.lastProcessedBlock}`);
    console.log(`   - RPC地址: ${status.providerUrl}`);

    // 3. 网络重试机制测试
    console.log('\n🌐 测试网络重试机制...');
    try {
      // 这里我们无法模拟网络错误，但可以测试正常的网络操作
      console.log('   执行网络操作测试...');
      await phrsDepositService.testProcessBlocks(13805521);
      console.log('   ✅ 网络操作正常');
    } catch (error: any) {
      console.log(`   ⚠️  网络操作遇到错误: ${error.message}`);
    }

    // 4. 并发处理测试
    console.log('\n🔄 测试并发处理保护...');
    const concurrentPromises = [];
    for (let i = 0; i < 5; i++) {
      concurrentPromises.push(
        phrsDepositService.testProcessBlocks(13805521).catch(error => {
          return `concurrent-${i}-error: ${error.message}`;
        })
      );
    }

    const concurrentResults = await Promise.all(concurrentPromises);
    console.log('📊 并发处理结果:');
    concurrentResults.forEach((result, index) => {
      if (typeof result === 'string' && result.includes('error')) {
        console.log(`   ${index + 1}. ⚠️  ${result}`);
      } else {
        console.log(`   ${index + 1}. ✅ 成功完成`);
      }
    });

    // 5. 服务启停测试
    console.log('\n🔄 测试服务启停机制...');
    
    if (!phrsDepositService.getStatus().isListening) {
      console.log('   启动监控服务...');
      await phrsDepositService.startListening();
      console.log('   ✅ 服务已启动');
      
      // 等待几秒钟
      console.log('   等待5秒钟观察运行状态...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      console.log('   停止监控服务...');
      phrsDepositService.stopListening();
      console.log('   ✅ 服务已停止');
    } else {
      console.log('   服务已在运行中，跳过启停测试');
    }

    // 6. 最终健康检查
    console.log('\n🏥 最终健康检查...');
    const finalHealthStatus = await phrsDepositService.healthCheck();
    console.log(`📊 最终健康状态: ${finalHealthStatus.status}`);

    // 7. 总结
    console.log('\n📋 测试总结:');
    console.log(`   - 健康检查: ${healthStatus.status === 'healthy' ? '✅ 通过' : '⚠️  需要关注'}`);
    console.log(`   - 网络连接: ${healthStatus.details.networkConnection ? '✅ 正常' : '❌ 异常'}`);
    console.log(`   - 数据库连接: ${healthStatus.details.databaseConnection ? '✅ 正常' : '❌ 异常'}`);
    console.log(`   - 并发处理: ✅ 已测试`);
    console.log(`   - 服务控制: ✅ 已测试`);

    console.log('\n🎉 健壮性测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});
