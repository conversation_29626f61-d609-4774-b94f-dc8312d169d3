#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB, sequelize } from '../config/db';

// 加载环境变量
dotenv.config();

async function main() {
  console.log('🔧 修复 phrs_deposits 表结构');
  console.log('=============================');

  try {
    // 连接数据库
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 修改 walletId 字段，允许为空
    console.log('🔄 步骤1: 删除外键约束...');

    try {
      await sequelize.query(`ALTER TABLE phrs_deposits DROP FOREIGN KEY phrs_deposits_ibfk_1`);
      console.log('✅ 外键约束已删除');
    } catch (error: any) {
      if (error.original?.code === 'ER_CANT_DROP_FIELD_OR_KEY') {
        console.log('⚠️  外键约束不存在，跳过删除');
      } else {
        throw error;
      }
    }

    console.log('🔄 步骤2: 修改字段为允许NULL...');
    await sequelize.query(`ALTER TABLE phrs_deposits MODIFY COLUMN walletId INT UNSIGNED NULL`);
    console.log('✅ 字段已修改为允许NULL');

    console.log('🔄 步骤3: 重新创建外键约束...');
    await sequelize.query(`
      ALTER TABLE phrs_deposits
      ADD CONSTRAINT phrs_deposits_wallet_fk
        FOREIGN KEY (walletId)
        REFERENCES user_wallets(id)
        ON DELETE SET NULL
        ON UPDATE CASCADE
    `);
    console.log('✅ 新的外键约束已创建');

    console.log('✅ 表结构修改完成');
    console.log('   - walletId 字段现在允许为空');
    console.log('   - 外键约束已更新为 ON DELETE SET NULL');

  } catch (error) {
    console.error('❌ 修改失败:', error);
    process.exit(1);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});
