#!/usr/bin/env ts-node

import dotenv from 'dotenv';
import { connectDB } from '../config/db';
import { phrsMonitorDebug } from '../debug/phrsMonitorDebug';

// 加载环境变量
dotenv.config();

async function main() {
  console.log('🔍 PHRS监控调试脚本');
  console.log('====================');

  try {
    // 连接数据库
    console.log('📊 连接数据库...');
    await connectDB();
    console.log('✅ 数据库连接成功');

    // 运行调试检查
    await phrsMonitorDebug.runFullDebug();

  } catch (error) {
    console.error('❌ 调试脚本执行失败:', error);
    process.exit(1);
  }

  process.exit(0);
}

// 运行主函数
main().catch((error) => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});
